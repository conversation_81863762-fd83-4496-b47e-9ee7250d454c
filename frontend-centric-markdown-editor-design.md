# Frontend-Centric Markdown Editor with TOC Design

## Overview

This document outlines a simplified, frontend-centric approach for editing large markdown documents (300+ pages) with table of contents navigation. The key principle is to leverage the frontend for editing and caching, only persisting the complete updated markdown to the backend when explicitly triggered.

## Design Philosophy

### Frontend-First Approach
- **Frontend handles**: TOC generation, block identification, editing, caching, and change management
- **Backend handles**: Document storage, retrieval, and persistence only
- **Simplicity**: Avoid complex block-based database schemas and APIs
- **Performance**: Leverage browser capabilities for fast interactions

### Core Workflow
1. Load complete markdown document from backend
2. Parse markdown to generate TOC and identify blocks on frontend
3. Enable click-to-edit functionality with frontend caching
4. Save complete updated markdown back to backend when triggered

## Current System Analysis

### Existing Frontend Components
- **TipTap Editor**: Already supports markdown with AST handling
- **Marked Library**: Used in `MarkdownViewer` for parsing
- **Mermaid Support**: Diagram rendering capability
- **Syntax Highlighting**: Code block highlighting with highlight.js

### Existing Backend Infrastructure
- **Tech Spec APIs**: Document CRUD operations in `src/api/routes/tech_spec.py`
- **GCS Storage**: File storage with presigned URLs
- **Document Models**: `TechnicalSpec` model for document management

## Data Models

### Frontend TypeScript Interfaces

```typescript
// Enhanced types for frontend-only block management
export interface MarkdownBlock {
    id: string;                    // Generated on frontend
    type: 'heading' | 'paragraph' | 'list' | 'code' | 'table' | 'blockquote' | 'image';
    level?: number;                // 1-6 for headings
    title?: string;                // For headings
    content: string;               // Raw markdown content
    startLine: number;             // Line number in original markdown
    endLine: number;               // End line number
    startOffset: number;           // Character offset in document
    endOffset: number;             // End character offset
    isModified: boolean;           // Track changes
    originalContent: string;       // For change detection
}

export interface TOCItem {
    id: string;
    blockId: string;
    title: string;
    level: number;                 // 1-6 for heading levels
    anchor: string;                // URL fragment
    children: TOCItem[];           // Nested structure
    lineNumber: number;            // For navigation
}

export interface DocumentState {
    id: string;
    title: string;
    originalMarkdown: string;      // Original content from backend
    currentMarkdown: string;       // Current state with edits
    blocks: MarkdownBlock[];       // Parsed blocks
    tableOfContents: TOCItem[];    // Generated TOC
    hasUnsavedChanges: boolean;    // Change tracking
    version: number;               // For conflict detection
    lastSaved: Date;
}

export interface EditSession {
    blockId: string;
    originalContent: string;
    currentContent: string;
    isActive: boolean;
    startTime: Date;
}
```

### Backend Models (Minimal Changes)

```python
# Reuse existing TechnicalSpec model or create simple MarkdownDocument
class MarkdownDocument(Base):
    __tablename__ = 'markdown_documents'
    
    id = Column(String, primary_key=True)
    project_id = Column(String, ForeignKey('projects.id'), nullable=False)
    title = Column(String, nullable=False)
    content = Column(Text, nullable=False)  # Full markdown content
    version = Column(Integer, default=1)
    checksum = Column(String, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
```

## Frontend Implementation

### 1. TOC Generator Service

```typescript
// src/services/TOCGeneratorService.ts
import { marked } from 'marked';

export class TOCGeneratorService {
    generateTOC(markdown: string): TOCItem[] {
        const tokens = marked.lexer(markdown);
        const tocItems: TOCItem[] = [];
        const stack: TOCItem[] = [];
        let lineNumber = 1;

        for (const token of tokens) {
            if (token.type === 'heading') {
                const tocItem: TOCItem = {
                    id: this.generateId(),
                    blockId: this.generateBlockId(token),
                    title: token.text,
                    level: token.depth,
                    anchor: this.generateAnchor(token.text),
                    children: [],
                    lineNumber
                };

                // Build hierarchical structure
                while (stack.length > 0 && stack[stack.length - 1].level >= token.depth) {
                    stack.pop();
                }

                if (stack.length === 0) {
                    tocItems.push(tocItem);
                } else {
                    stack[stack.length - 1].children.push(tocItem);
                }

                stack.push(tocItem);
            }
            
            lineNumber += this.countLines(token);
        }

        return tocItems;
    }

    private generateAnchor(text: string): string {
        return text.toLowerCase()
            .replace(/[^\w\s-]/g, '')
            .replace(/\s+/g, '-')
            .trim();
    }
}
```

### 2. Block Parser Service

```typescript
// src/services/BlockParserService.ts
export class BlockParserService {
    parseMarkdownToBlocks(markdown: string): MarkdownBlock[] {
        const tokens = marked.lexer(markdown);
        const blocks: MarkdownBlock[] = [];
        let currentOffset = 0;
        let currentLine = 1;

        for (const token of tokens) {
            const block: MarkdownBlock = {
                id: this.generateBlockId(),
                type: this.mapTokenType(token.type),
                level: token.type === 'heading' ? token.depth : undefined,
                title: token.type === 'heading' ? token.text : undefined,
                content: this.extractContent(token),
                startLine: currentLine,
                endLine: currentLine + this.countLines(token) - 1,
                startOffset: currentOffset,
                endOffset: currentOffset + this.extractContent(token).length,
                isModified: false,
                originalContent: this.extractContent(token)
            };

            blocks.push(block);
            currentOffset = block.endOffset + 2; // +2 for \n\n between blocks
            currentLine = block.endLine + 1;
        }

        return blocks;
    }

    private mapTokenType(tokenType: string): MarkdownBlock['type'] {
        const typeMap: Record<string, MarkdownBlock['type']> = {
            'heading': 'heading',
            'paragraph': 'paragraph',
            'list': 'list',
            'code': 'code',
            'table': 'table',
            'blockquote': 'blockquote'
        };
        return typeMap[tokenType] || 'paragraph';
    }
}
```

### 3. Document State Manager

```typescript
// src/hooks/useMarkdownDocument.ts
export const useMarkdownDocument = (documentId: string) => {
    const [documentState, setDocumentState] = useState<DocumentState | null>(null);
    const [editSession, setEditSession] = useState<EditSession | null>(null);
    const [isLoading, setIsLoading] = useState(false);

    const tocGenerator = new TOCGeneratorService();
    const blockParser = new BlockParserService();

    const loadDocument = async () => {
        setIsLoading(true);
        try {
            const response = await fetch(`/api/documents/${documentId}`);
            const data = await response.json();
            
            const blocks = blockParser.parseMarkdownToBlocks(data.content);
            const toc = tocGenerator.generateTOC(data.content);

            setDocumentState({
                id: data.id,
                title: data.title,
                originalMarkdown: data.content,
                currentMarkdown: data.content,
                blocks,
                tableOfContents: toc,
                hasUnsavedChanges: false,
                version: data.version,
                lastSaved: new Date(data.updated_at)
            });
        } catch (error) {
            console.error('Failed to load document:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const startEditingBlock = (blockId: string) => {
        const block = documentState?.blocks.find(b => b.id === blockId);
        if (!block) return;

        setEditSession({
            blockId,
            originalContent: block.content,
            currentContent: block.content,
            isActive: true,
            startTime: new Date()
        });
    };

    const updateBlockContent = (blockId: string, newContent: string) => {
        if (!documentState) return;

        const updatedBlocks = documentState.blocks.map(block => {
            if (block.id === blockId) {
                return {
                    ...block,
                    content: newContent,
                    isModified: newContent !== block.originalContent
                };
            }
            return block;
        });

        // Rebuild full markdown from blocks
        const newMarkdown = this.rebuildMarkdownFromBlocks(updatedBlocks);
        
        setDocumentState({
            ...documentState,
            blocks: updatedBlocks,
            currentMarkdown: newMarkdown,
            hasUnsavedChanges: newMarkdown !== documentState.originalMarkdown
        });

        // Update edit session
        if (editSession?.blockId === blockId) {
            setEditSession({
                ...editSession,
                currentContent: newContent
            });
        }
    };

    const saveDocument = async () => {
        if (!documentState?.hasUnsavedChanges) return;

        try {
            const response = await fetch(`/api/documents/${documentId}`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    content: documentState.currentMarkdown,
                    version: documentState.version
                })
            });

            if (response.ok) {
                const updatedDoc = await response.json();
                setDocumentState({
                    ...documentState,
                    originalMarkdown: documentState.currentMarkdown,
                    hasUnsavedChanges: false,
                    version: updatedDoc.version,
                    lastSaved: new Date()
                });
            }
        } catch (error) {
            console.error('Failed to save document:', error);
        }
    };

    return {
        documentState,
        editSession,
        isLoading,
        loadDocument,
        startEditingBlock,
        updateBlockContent,
        saveDocument,
        cancelEdit: () => setEditSession(null)
    };
};
```

## Architecture Diagrams

### System Architecture

```mermaid
graph TB
    subgraph "Frontend (React/TypeScript)"
        A[Document Loader] --> B[Markdown Parser]
        B --> C[TOC Generator]
        B --> D[Block Parser]
        C --> E[TOC Navigation]
        D --> F[Block Editor]
        F --> G[Change Cache]
        G --> H[Save Manager]
        E --> I[Scroll Navigation]
        F --> I
    end

    subgraph "Backend (Python/Flask)"
        J[Document API]
        K[File Storage]
        L[Version Control]
    end

    A --> |GET /documents/{id}| J
    H --> |PUT /documents/{id}| J
    J --> K
    J --> L

    style A fill:#e3f2fd
    style F fill:#fce4ec
    style G fill:#f1f8e9
    style H fill:#fff3e0
```

### Data Flow Sequence

```mermaid
sequenceDiagram
    participant U as User
    participant TOC as TOC Panel
    participant E as Block Editor
    participant C as Change Cache
    participant API as Backend API

    Note over U,API: Document Loading
    U->>TOC: Open document
    TOC->>API: GET /documents/{id}
    API-->>TOC: Full markdown content
    TOC->>TOC: Parse markdown to blocks & TOC
    TOC->>U: Display TOC + document

    Note over U,API: Block Editing
    U->>TOC: Click TOC item
    TOC->>E: Navigate to block
    U->>E: Click edit
    E->>E: Open inline editor
    U->>E: Edit content
    E->>C: Cache changes (debounced)
    
    Note over U,API: Saving
    U->>E: Trigger save
    E->>C: Rebuild full markdown
    C->>API: PUT /documents/{id} (full content)
    API-->>C: Success + new version
    C->>E: Update state
```

## API Design (Simplified)

### REST Endpoints

```python
# Minimal API endpoints in existing tech_spec.py or new markdown.py

@app.route("/api/documents/<document_id>", methods=["GET"])
def get_document(document_id: str):
    """Get full markdown document"""
    return {
        "id": document_id,
        "title": "Document Title",
        "content": "# Full markdown content...",
        "version": 1,
        "updated_at": "2024-01-01T00:00:00Z"
    }

@app.route("/api/documents/<document_id>", methods=["PUT"])
def update_document(document_id: str):
    """Update full markdown document"""
    data = request.json
    # Save full content, increment version
    return {
        "id": document_id,
        "version": 2,
        "updated_at": "2024-01-01T00:00:00Z"
    }
```

## Implementation Plan

### Phase 1: Core Frontend Components (Week 1-2)
- [ ] TOC generator service using marked.js
- [ ] Block parser service for content identification
- [ ] Document state management hook
- [ ] Basic TOC navigation component

### Phase 2: Editing Interface (Week 2-3)
- [ ] Inline block editor with TipTap
- [ ] Click-to-edit functionality
- [ ] Change caching and state management
- [ ] Auto-save with debouncing

### Phase 3: Backend Integration (Week 3-4)
- [ ] Simple document API endpoints
- [ ] Version conflict detection
- [ ] Save/load functionality
- [ ] Error handling and user feedback

### Phase 4: Polish & Optimization (Week 4-5)
- [ ] Performance optimization for large documents
- [ ] Virtual scrolling for 300+ pages
- [ ] Accessibility improvements
- [ ] Comprehensive testing

## Benefits of This Approach

1. **Simplicity**: No complex block-based database schema
2. **Performance**: Frontend parsing and caching for fast interactions
3. **Reliability**: Full document saves reduce data corruption risks
4. **Maintainability**: Leverages existing markdown ecosystem
5. **Flexibility**: Easy to extend with additional markdown features

## Technical Considerations

### Performance for Large Documents
- Use virtual scrolling for 300+ page documents
- Implement lazy loading for TOC sections
- Debounce parsing operations
- Cache parsed results in browser storage

### Change Management
- Track modifications at block level
- Implement undo/redo functionality
- Auto-save with configurable intervals
- Conflict detection on save

### Browser Compatibility
- Leverage modern browser APIs for performance
- Fallback strategies for older browsers
- Progressive enhancement approach

## Component Implementation Details

### 1. TOC Navigation Component

```typescript
// src/components/markdown/TOCNavigationPanel.tsx
interface TOCNavigationProps {
    tableOfContents: TOCItem[];
    activeBlockId: string | null;
    onNavigateToBlock: (blockId: string) => void;
    searchQuery?: string;
}

export const TOCNavigationPanel: React.FC<TOCNavigationProps> = ({
    tableOfContents,
    activeBlockId,
    onNavigateToBlock,
    searchQuery = ''
}) => {
    const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
    const [filteredTOC, setFilteredTOC] = useState(tableOfContents);

    // Filter TOC based on search query
    useEffect(() => {
        if (!searchQuery) {
            setFilteredTOC(tableOfContents);
            return;
        }

        const filterItems = (items: TOCItem[]): TOCItem[] => {
            return items.reduce((acc, item) => {
                const matchesSearch = item.title.toLowerCase().includes(searchQuery.toLowerCase());
                const filteredChildren = filterItems(item.children);

                if (matchesSearch || filteredChildren.length > 0) {
                    acc.push({
                        ...item,
                        children: filteredChildren
                    });
                }
                return acc;
            }, [] as TOCItem[]);
        };

        setFilteredTOC(filterItems(tableOfContents));
    }, [tableOfContents, searchQuery]);

    const toggleExpanded = (itemId: string) => {
        setExpandedItems(prev => {
            const newSet = new Set(prev);
            if (newSet.has(itemId)) {
                newSet.delete(itemId);
            } else {
                newSet.add(itemId);
            }
            return newSet;
        });
    };

    return (
        <div className="toc-navigation-panel">
            <div className="toc-header">
                <h3>Table of Contents</h3>
                <input
                    type="text"
                    placeholder="Search sections..."
                    className="toc-search"
                    defaultValue={searchQuery}
                />
            </div>

            <div className="toc-tree">
                {filteredTOC.map(item => (
                    <TOCTreeItem
                        key={item.id}
                        item={item}
                        activeBlockId={activeBlockId}
                        expandedItems={expandedItems}
                        onToggleExpanded={toggleExpanded}
                        onNavigate={onNavigateToBlock}
                        level={0}
                    />
                ))}
            </div>
        </div>
    );
};

const TOCTreeItem: React.FC<{
    item: TOCItem;
    activeBlockId: string | null;
    expandedItems: Set<string>;
    onToggleExpanded: (id: string) => void;
    onNavigate: (blockId: string) => void;
    level: number;
}> = ({ item, activeBlockId, expandedItems, onToggleExpanded, onNavigate, level }) => {
    const isActive = activeBlockId === item.blockId;
    const isExpanded = expandedItems.has(item.id);
    const hasChildren = item.children.length > 0;

    return (
        <div className={`toc-item level-${level}`}>
            <div
                className={`toc-item-content ${isActive ? 'active' : ''}`}
                onClick={() => onNavigate(item.blockId)}
            >
                {hasChildren && (
                    <button
                        className={`expand-button ${isExpanded ? 'expanded' : ''}`}
                        onClick={(e) => {
                            e.stopPropagation();
                            onToggleExpanded(item.id);
                        }}
                    >
                        ▶
                    </button>
                )}
                <span className={`heading-level-${item.level}`}>
                    {item.title}
                </span>
            </div>

            {hasChildren && isExpanded && (
                <div className="toc-children">
                    {item.children.map(child => (
                        <TOCTreeItem
                            key={child.id}
                            item={child}
                            activeBlockId={activeBlockId}
                            expandedItems={expandedItems}
                            onToggleExpanded={onToggleExpanded}
                            onNavigate={onNavigate}
                            level={level + 1}
                        />
                    ))}
                </div>
            )}
        </div>
    );
};
```

### 2. Document Viewer with Click-to-Edit

```typescript
// src/components/markdown/MarkdownDocumentViewer.tsx
interface DocumentViewerProps {
    documentState: DocumentState;
    editingBlockId: string | null;
    onStartEdit: (blockId: string) => void;
    onUpdateBlock: (blockId: string, content: string) => void;
    onCancelEdit: () => void;
}

export const MarkdownDocumentViewer: React.FC<DocumentViewerProps> = ({
    documentState,
    editingBlockId,
    onStartEdit,
    onUpdateBlock,
    onCancelEdit
}) => {
    const containerRef = useRef<HTMLDivElement>(null);

    const scrollToBlock = (blockId: string) => {
        const blockElement = containerRef.current?.querySelector(`[data-block-id="${blockId}"]`);
        if (blockElement) {
            blockElement.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });
        }
    };

    return (
        <div ref={containerRef} className="document-viewer">
            {documentState.blocks.map(block => (
                <DocumentBlock
                    key={block.id}
                    block={block}
                    isEditing={editingBlockId === block.id}
                    onStartEdit={() => onStartEdit(block.id)}
                    onUpdateContent={(content) => onUpdateBlock(block.id, content)}
                    onCancelEdit={onCancelEdit}
                />
            ))}
        </div>
    );
};

const DocumentBlock: React.FC<{
    block: MarkdownBlock;
    isEditing: boolean;
    onStartEdit: () => void;
    onUpdateContent: (content: string) => void;
    onCancelEdit: () => void;
}> = ({ block, isEditing, onStartEdit, onUpdateContent, onCancelEdit }) => {
    const [editContent, setEditContent] = useState(block.content);

    useEffect(() => {
        setEditContent(block.content);
    }, [block.content]);

    const handleSave = () => {
        onUpdateContent(editContent);
    };

    const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
            onCancelEdit();
        } else if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
            handleSave();
        }
    };

    if (isEditing) {
        return (
            <div
                className="document-block editing"
                data-block-id={block.id}
                onKeyDown={handleKeyDown}
            >
                <div className="edit-toolbar">
                    <span className="block-type">{block.type}</span>
                    <div className="edit-actions">
                        <button onClick={handleSave} className="save-btn">
                            Save (Ctrl+Enter)
                        </button>
                        <button onClick={onCancelEdit} className="cancel-btn">
                            Cancel (Esc)
                        </button>
                    </div>
                </div>

                <TiptapEditor
                    markdown={editContent}
                    onChange={setEditContent}
                    editable={true}
                    className="block-editor"
                />
            </div>
        );
    }

    return (
        <div
            className={`document-block ${block.isModified ? 'modified' : ''}`}
            data-block-id={block.id}
            onClick={onStartEdit}
        >
            <div className="block-content">
                <MarkdownViewer markdown={block.content} />
            </div>
            <div className="edit-overlay">
                <button className="edit-button">
                    ✏️ Edit
                </button>
            </div>
        </div>
    );
};
```

### 3. Main Document Container

```typescript
// src/components/markdown/MarkdownDocumentContainer.tsx
export const MarkdownDocumentContainer: React.FC<{
    documentId: string;
}> = ({ documentId }) => {
    const {
        documentState,
        editSession,
        isLoading,
        loadDocument,
        startEditingBlock,
        updateBlockContent,
        saveDocument,
        cancelEdit
    } = useMarkdownDocument(documentId);

    const [selectedBlockId, setSelectedBlockId] = useState<string | null>(null);
    const [searchQuery, setSearchQuery] = useState('');

    useEffect(() => {
        loadDocument();
    }, [documentId]);

    const handleTOCNavigation = (blockId: string) => {
        setSelectedBlockId(blockId);
        // Scroll to block will be handled by the DocumentViewer
    };

    const handleSaveDocument = async () => {
        await saveDocument();
        // Show success notification
    };

    if (isLoading || !documentState) {
        return (
            <div className="loading-container">
                <div className="loading-spinner" />
                <p>Loading document...</p>
            </div>
        );
    }

    return (
        <div className="markdown-document-container">
            {/* Header with save controls */}
            <div className="document-header">
                <h1>{documentState.title}</h1>
                <div className="document-controls">
                    {documentState.hasUnsavedChanges && (
                        <span className="unsaved-indicator">
                            Unsaved changes
                        </span>
                    )}
                    <button
                        onClick={handleSaveDocument}
                        disabled={!documentState.hasUnsavedChanges}
                        className="save-document-btn"
                    >
                        Save Document
                    </button>
                </div>
            </div>

            <div className="document-layout">
                {/* TOC Sidebar */}
                <div className="toc-sidebar">
                    <TOCNavigationPanel
                        tableOfContents={documentState.tableOfContents}
                        activeBlockId={selectedBlockId}
                        onNavigateToBlock={handleTOCNavigation}
                        searchQuery={searchQuery}
                    />
                </div>

                {/* Main Content Area */}
                <div className="document-content">
                    <MarkdownDocumentViewer
                        documentState={documentState}
                        editingBlockId={editSession?.blockId || null}
                        onStartEdit={startEditingBlock}
                        onUpdateBlock={updateBlockContent}
                        onCancelEdit={cancelEdit}
                    />
                </div>
            </div>
        </div>
    );
};
```

## Styling and UX Considerations

### CSS Structure

```css
/* src/components/markdown/MarkdownEditor.css */
.markdown-document-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: #fafafa;
}

.document-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background: white;
    border-bottom: 1px solid #e0e0e0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.document-layout {
    display: flex;
    flex: 1;
    overflow: hidden;
}

.toc-sidebar {
    width: 300px;
    background: white;
    border-right: 1px solid #e0e0e0;
    overflow-y: auto;
    padding: 1rem;
}

.document-content {
    flex: 1;
    overflow-y: auto;
    padding: 2rem;
    background: white;
    margin: 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* TOC Styles */
.toc-navigation-panel {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.toc-search {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 1rem;
}

.toc-item {
    margin: 0.25rem 0;
}

.toc-item-content {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.toc-item-content:hover {
    background-color: #f5f5f5;
}

.toc-item-content.active {
    background-color: #e3f2fd;
    color: #1976d2;
    font-weight: 500;
}

/* Block Editor Styles */
.document-block {
    position: relative;
    margin: 1rem 0;
    padding: 1rem;
    border: 2px solid transparent;
    border-radius: 4px;
    transition: border-color 0.2s;
}

.document-block:hover {
    border-color: #e0e0e0;
}

.document-block.editing {
    border-color: #1976d2;
    background-color: #fafafa;
}

.document-block.modified {
    border-left: 4px solid #ff9800;
}

.edit-overlay {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    opacity: 0;
    transition: opacity 0.2s;
}

.document-block:hover .edit-overlay {
    opacity: 1;
}

.edit-button {
    background: #1976d2;
    color: white;
    border: none;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .document-layout {
        flex-direction: column;
    }

    .toc-sidebar {
        width: 100%;
        height: 200px;
        border-right: none;
        border-bottom: 1px solid #e0e0e0;
    }
}
```

## Performance Optimizations

### Virtual Scrolling for Large Documents

```typescript
// src/components/markdown/VirtualizedDocumentViewer.tsx
import { FixedSizeList as List } from 'react-window';

export const VirtualizedDocumentViewer: React.FC<{
    blocks: MarkdownBlock[];
    itemHeight: number;
}> = ({ blocks, itemHeight = 200 }) => {
    const Row = ({ index, style }: { index: number; style: React.CSSProperties }) => (
        <div style={style}>
            <DocumentBlock
                block={blocks[index]}
                // ... other props
            />
        </div>
    );

    return (
        <List
            height={600}
            itemCount={blocks.length}
            itemSize={itemHeight}
            overscanCount={5}
        >
            {Row}
        </List>
    );
};
```

### Debounced Auto-save

```typescript
// src/hooks/useAutoSave.ts
export const useAutoSave = (
    documentState: DocumentState,
    saveFunction: () => Promise<void>,
    delay: number = 30000 // 30 seconds
) => {
    const debouncedSave = useMemo(
        () => debounce(saveFunction, delay),
        [saveFunction, delay]
    );

    useEffect(() => {
        if (documentState.hasUnsavedChanges) {
            debouncedSave();
        }
    }, [documentState.hasUnsavedChanges, debouncedSave]);

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            debouncedSave.cancel();
        };
    }, [debouncedSave]);
};
```

## Testing Strategy

### Unit Tests

```typescript
// src/components/markdown/__tests__/TOCGenerator.test.ts
describe('TOCGeneratorService', () => {
    const service = new TOCGeneratorService();

    test('generates correct TOC structure', () => {
        const markdown = `
# Chapter 1
## Section 1.1
### Subsection 1.1.1
## Section 1.2
# Chapter 2
        `;

        const toc = service.generateTOC(markdown);

        expect(toc).toHaveLength(2);
        expect(toc[0].title).toBe('Chapter 1');
        expect(toc[0].children).toHaveLength(2);
        expect(toc[0].children[0].children).toHaveLength(1);
    });

    test('handles empty markdown', () => {
        const toc = service.generateTOC('');
        expect(toc).toHaveLength(0);
    });
});
```

### Integration Tests

```typescript
// src/components/markdown/__tests__/MarkdownEditor.integration.test.tsx
describe('Markdown Editor Integration', () => {
    test('full editing workflow', async () => {
        const { getByText, getByRole } = render(
            <MarkdownDocumentContainer documentId="test-doc" />
        );

        // Wait for document to load
        await waitFor(() => {
            expect(getByText('Chapter 1')).toBeInTheDocument();
        });

        // Click TOC item
        fireEvent.click(getByText('Chapter 1'));

        // Start editing
        fireEvent.click(getByRole('button', { name: /edit/i }));

        // Edit content
        const editor = getByRole('textbox');
        fireEvent.change(editor, { target: { value: '# Updated Chapter 1' } });

        // Save
        fireEvent.click(getByRole('button', { name: /save/i }));

        // Verify changes
        expect(getByText('Updated Chapter 1')).toBeInTheDocument();
    });
});
```

## Conclusion

This frontend-centric approach provides a simpler, more maintainable solution for editing large markdown documents with TOC navigation. By leveraging the frontend for parsing, caching, and change management, we can deliver a responsive user experience while keeping the backend simple and focused on document persistence.

The design emphasizes:
- **Simplicity**: Minimal backend changes, leveraging existing infrastructure
- **Performance**: Frontend parsing and virtual scrolling for large documents
- **User Experience**: Intuitive TOC navigation and inline editing
- **Maintainability**: Clean separation of concerns and testable components
- **Scalability**: Efficient handling of 300+ page documents
```
